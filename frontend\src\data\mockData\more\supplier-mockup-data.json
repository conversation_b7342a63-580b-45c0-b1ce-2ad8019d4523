[{"id": 1, "name": "Amazon", "address": "410 Terry Ave N", "city": "Seattle", "state": "Washington", "zip": "9810", "country": "United States", "contact_name": "<PERSON>", "contact_phone": "**************", "supplier_contact_fax": "", "contact_email": "<EMAIL>", "url": "https://amazon.com", "logo": "https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg", "notes": "Always use the corporate account for a 10% discount.", "created_at": "2025-10-22"}, {"id": 2, "name": "Newegg", "address": "17560 Rowland St", "city": "City of Industry", "state": "California", "zip": "9174", "country": "United States", "contact_name": "<PERSON>", "contact_phone": "**************", "supplier_contact_fax": "", "contact_email": "<EMAIL>", "url": "https://newegg.com", "logo": "https://upload.wikimedia.org/wikipedia/commons/4/4a/Newegg_Logo.svg", "notes": "", "created_at": "2025-10-22"}, {"id": 3, "name": "<PERSON><PERSON><PERSON>", "address": "500 8th Ave", "city": "New York", "state": "New York", "zip": "1008", "country": "United States", "contact_name": "<PERSON>", "contact_phone": "**************", "supplier_contact_fax": "", "contact_email": "<EMAIL>", "url": "https://staples.com", "logo": "https://upload.wikimedia.org/wikipedia/commons/3/3a/Staples_Logo.svg", "notes": "Shop from the store in Manhattan for the best discount.", "created_at": "2025-10-22"}, {"id": 4, "name": "<PERSON><PERSON><PERSON>", "address": "115 Buckingham Palace Rd", "city": "London", "state": "", "zip": "8493", "country": "United Kingdom", "contact_name": "<PERSON>", "contact_phone": "+442079320805", "supplier_contact_fax": "", "contact_email": "<EMAIL>", "url": "https://www.whsmith.co.uk/", "logo": "https://upload.wikimedia.org/wikipedia/commons/8/89/WHSmith_Logo.svg", "notes": "", "created_at": "2025-10-22"}]